import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { HomeComponent } from './home/<USER>/home.component';
import {
  CustomerSearchComponent,
  CustomerInfoComponent,
  LandingComponent,
  AssetInfoComponent,
  AssetListComponent,
  AssetSearchComponent,
  NewAssetComponent,
  GenericInfoComponent,
  ConfirmComponent,
  SummaryComponent,
  WizardContainerComponent
} from './simulation';
import { GuaranteeInfoComponent } from './appraisal-request/components/guarantee-info/guarantee-info.component';
import { DashboardComponent } from './dashboard/components/dashboard.component';
import { AdvancedSearchComponent } from './search-page/component/advanced-search/advanced-search.component';
import { FastSearchComponent } from './search-page/component/fast-search/fast-search.component';
import { WizardDetailComponent } from './wizard-detail';
import { AppraisalInfoComponent } from './appraisal-request/components/appraisal-info/appraisal-info.component';
import { ActivitiesDetailComponent } from './shared/activities-detail/component/activities-detail.component';
import { GenericTaskComponent, UploadDocsComponent } from './tasks';
import { GenericDataComponent } from './appraisal-compilation/components/generic-data/generic-data.component';
import { GuaranteesComponent } from './appraisal-compilation/components/guarantees/guarantees.component';
import { EvaluationsComponent } from './appraisal-compilation/components/evaluations/evaluations.component';
import { SALComponent } from './appraisal-compilation/components/sal/sal.component';
import { AppraisalFillConfirmComponent } from './appraisal-compilation/components/appraisal-fill-confirm/appraisal-fill-confirm.component';
import { QteComponent } from './appraisal-compilation/components/qte/qte.component';
import { AgrarianAgencyComponent } from './appraisal-compilation/components/agrarian-agency/agrarian-agency.component';
import { RegisterComponent } from './appraisal-compilation/components/register/register.component';
import { GoodDescriptionComponent } from './appraisal-compilation/components/good-description/good-description.component';
import { ThirdOpinionComponent } from './appraisal-compilation/components/third-opinion/third-opinion.component';
import { RestrictionsComponent } from './appraisal-compilation/components/restrictions/restrictions.component';
import { ConfigurationComponent } from './configuration/component/configuration.component';
import { RegistryComponent } from './registry/registry.component';
import { ExpertFirmInfoComponent } from './registry/children/expert-firm-info/expert-firm-info.component';
import { SampleChecksTableComponent } from './configuration/component/sample-checks-table/sample-checks-table.component';
import { LoanAdministationSampleChecksComponent } from './dashboard/components/loan-administration-sample-checks/loan-administation-sample-checks.component';
import { MassiveUploadComponent } from './massive-upload/massive-upload.component';
import { UploadAssetListComponent } from './massive-upload/upload-asset-list/upload-asset-list.component';
import {
  GuaranteeTransferConfirmComponent,
  StartGuaranteesComponent,
  TargetGuaranteesComponent,
  NdgSearchComponent
} from './guarantee-transfer';
import { AuthGuard, AccessRightsGuard, DeactivateGuard } from './guards';
import {} from './guards/access-rights/access-rights.guard';
import { AppraisalMigrationComponent } from './appraisal-migration/components/appraisal-migration.component';
import { AppraisalCtuComponent } from './appraisal-ctu/components/appraisal-ctu.component';
import { UploadListComponent } from './massive-upload/upload-list/upload-list.component';
import { RedirectComponent } from './redirect';
import { GuaranteeAssetListComponent } from './guarantee-asset-list/guarantee-asset-list.component';
import {
  GuaranteeFractionationComponent,
  GuaranteeOnArrivalComponent,
  GuaranteeSummaryComponent
} from './guarantee-fractionation';
import { SelectAppraisalsComponent } from './guarantee-transfer/components/select-appraisals/select-appraisals.component';
import { GuaranteeTransferComponent } from './guarantee-transfer/components/guarantee-transfer.component';
import { ExternalMortageComponent } from './external-mortage/external-mortage.component';
import { ExternalMortageProcessComponent } from './external-mortage/components/external-mortage-process/external-mortage-process.component';
import { DocumentSuspendComponent } from './document-suspend/components/document-suspend.component';
import { AmeoriPageComponent } from './ameori/ameori-page.component';
import { SearchServicingComponent } from './search-servicing/search-servicing.component';
import { DocumentComponent } from './search-servicing/components/document/document.component';
import { PaymentComponent } from './simulation/components/payment/payment.component';
import { SilosInterfaceComponent } from './new-Silos/silos-interface/silos-interface.component';

const routes: Routes = [
  { path: '', redirectTo: '/index', pathMatch: 'full' },
  {
    path: 'index',
    data: { name: 'index' },
    component: HomeComponent,
    canActivate: [AuthGuard]
  },
  {
    path: 'ameori',
    data: { name: 'ameori-page' },
    component: AmeoriPageComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'activities-detail',
    data: { name: 'activities-detail' },
    component: ActivitiesDetailComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'generic-task/:positionId/:taskId/:taskCod',
    data: { name: 'generic-task' },
    component: GenericTaskComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'dashboard/:dashboardType',
    data: { name: 'dashboard' },
    component: DashboardComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'wizard-detail/:wizardCode/:positionId',
    data: { name: 'wizard-detail' },
    component: WizardDetailComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'wizard-detail/:wizardCode/:positionId/:taskId/:taskCod',
    data: { name: 'wizard-detail' },
    component: WizardDetailComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'customer-search/:wizardCode/:positionId/:famAsset/:requestType',
    data: { name: 'customer-search' },
    component: CustomerSearchComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'landing/:wizardCode/:positionId',
    data: { name: 'landing' },
    component: LandingComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'landing/:wizardCode/:positionId/:taskId/:taskCod/:readOnly',
    data: { name: 'landing' },
    component: LandingComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'massive-upload',
    data: { name: 'massive-upload' },
    component: MassiveUploadComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'upload-list/:loadId',
    data: { name: 'upload-list' },
    component: UploadListComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'upload-asset-list/:loadId',
    data: { name: 'upload-asset-list' },
    component: UploadAssetListComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  // Wizard Sostituzione Garanzia
  {
    path: 'ndgSearch',
    component: NdgSearchComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'guaranteeTransfer/:ndgOrigin/:ndgDestination',
    data: { name: 'guaranteeTransferWizard' },
    canActivate: [AuthGuard],
    component: GuaranteeTransferComponent,
    children: [
      {
        path: '',
        canActivateChild: [AuthGuard],
        children: [
          {
            path: 'select-appraisals',
            data: { name: 'guaranteeTransferWizard' },
            component: SelectAppraisalsComponent,
            canActivate: [AccessRightsGuard]
          },
          {
            path: 'start-guarantees',
            data: { name: 'guaranteeTransferWizard' },
            component: StartGuaranteesComponent,
            canActivate: [AccessRightsGuard]
          },
          {
            path: 'target-guarantees',
            data: { name: 'guaranteeTransferWizard' },
            component: TargetGuaranteesComponent,
            canActivate: [AccessRightsGuard]
          },
          {
            path: 'guarantees-transfer-confirm',
            data: { name: 'guaranteeTransferWizard' },
            component: GuaranteeTransferConfirmComponent,
            canActivate: [AccessRightsGuard]
          }
        ]
      }
    ]
  },
  // --------------------------------------
  // Wizard frazionamento garanzia
  {
    path: 'guaranteeFractionation/:familyAsset/:ndg',
    data: { name: 'guaranteeFractionationWizard' },
    component: GuaranteeFractionationComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: 'guarantee-info',
        data: { name: 'guaranteeFractionationWizard' },
        component: GuaranteeInfoComponent,
        canActivate: [AccessRightsGuard]
      },
      {
        path: 'asset-choice',
        data: { name: 'guaranteeFractionationWizard' },
        component: GuaranteeAssetListComponent,
        canActivate: [AccessRightsGuard]
      },
      {
        path: 'guarantee-on-arrival',
        data: { name: 'guaranteeFractionationWizard' },
        component: GuaranteeOnArrivalComponent,
        canActivate: [AccessRightsGuard]
      },
      {
        path: 'summary',
        data: { name: 'guaranteeFractionationWizard' },
        component: GuaranteeSummaryComponent,
        canActivate: [AccessRightsGuard],
        canDeactivate: [DeactivateGuard]
      }
    ]
  },
  // ---------------------------------------
  {
    path: 'wizard/:wizardCode/:positionId',
    data: { name: 'wizard' },
    component: WizardContainerComponent,
    canActivate: [AuthGuard],
    children: [
      // Compilazione della richiesta
      {
        path: '',
        canActivateChild: [AuthGuard],
        children: [
          {
            path: 'customer-info',
            data: { name: 'customer-info' },
            component: CustomerInfoComponent,
            canActivate: [AccessRightsGuard]
          },

           {
            path: 'guarantee-info',
            component: GuaranteeInfoComponent,
            canActivate: [AccessRightsGuard]
          },
        
          {
            path: 'asset-info',
            data: { name: 'asset-info' },
            component: AssetInfoComponent,
            canActivate: [AccessRightsGuard],
            children: [
              {
                path: 'asset-list',
                component: AssetListComponent
              },
              {
                path: 'appraisal-list',
                component: AppraisalInfoComponent
              },
              {
                path: 'asset-search',
                component: AssetSearchComponent
              },
              {
                path: 'new-asset',
                component: NewAssetComponent
              }
            ]
          },
          {
            path: 'generic-info',
            data: { name: 'generic-info' },
            component: GenericInfoComponent,
            canActivate: [AccessRightsGuard]
          },
          {
            path: 'confirm',
            data: { name: 'confirm' },
            component: ConfirmComponent,
            canActivate: [AccessRightsGuard]
          },
          {
            path: 'summary',
            data: { name: 'summary' },
            component: SummaryComponent,
            canActivate: [AccessRightsGuard]
          },

          {
            path: 'payment',
            data: { name: 'payment' },
            component: PaymentComponent,
            canActivate: [AccessRightsGuard]
          }
       
        ]
      }
    ]
  },
  {
    path: 'task-wizard/:wizardCode/:positionId/:taskId/:taskCod/:readOnly',
    data: { name: 'task-wizard' },
    component: WizardContainerComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: '',
        canActivateChild: [AuthGuard],
        children: [
          // Compilazione della perizia
          {
            path: 'generic-data',
            data: { name: 'generic-data' },
            component: GenericDataComponent,
            canActivate: [AccessRightsGuard]
          },
          {
            path: 'guarantees',
            data: { name: 'guarantees' },
            component: GuaranteesComponent,
            canActivate: [AccessRightsGuard]
          },
          {
            path: 'evaluations',
            data: { name: 'evaluations' },
            component: EvaluationsComponent,
            canActivate: [AccessRightsGuard]
          },
          {
            path: 'sal',
            data: { name: 'sal' },
            component: SALComponent,
            canActivate: [AccessRightsGuard]
          },
          {
            path: 'qte',
            data: { name: 'qte' },
            component: QteComponent,
            canActivate: [AccessRightsGuard]
          },
          {
            path: 'agrarian-agency',
            data: { name: 'agrarian-agency' },
            component: AgrarianAgencyComponent,
            canActivate: [AccessRightsGuard]
          },
          {
            path: 'register',
            data: { name: 'register' },
            component: RegisterComponent,
            canActivate: [AccessRightsGuard]
          },
          {
            path: 'good-description',
            data: { name: 'good-description' },
            component: GoodDescriptionComponent,
            canActivate: [AccessRightsGuard]
          },
          {
            path: 'fill-confirm',
            data: { name: 'fill-confirm' },
            component: AppraisalFillConfirmComponent,
            canActivate: [AccessRightsGuard]
          },
          {
            path: 'third-opinion',
            data: { name: 'third-opinion' },
            component: ThirdOpinionComponent,
            canActivate: [AccessRightsGuard]
          },
          {
            path: 'restrictions',
            data: { name: 'restrictions' },
            component: RestrictionsComponent,
            canActivate: [AccessRightsGuard]
          }
        ]
      }
    ]
  },
  {
    path: 'advancedSearch',
    data: { name: 'advancedSearch' },
    component: AdvancedSearchComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'fastSearch',
    data: { name: 'fastSearch' },
    component: FastSearchComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'activities-detail',
    data: { name: 'activities-detail' },
    component: ActivitiesDetailComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'task-landing/:positionId/:taskId/:taskCod',
    data: { name: 'task-landing' },
    component: RedirectComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'upload-docs/:positionId/:taskId/:taskCod',
    data: { name: 'upload-docs' },
    component: UploadDocsComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'configuration/:configurationCode',
    data: { name: 'configuration' },
    component: ConfigurationComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'experts-landing',
    data: { name: 'experts-landing' },
    component: RedirectComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'registry',
    data: { name: 'registry' },
    component: RegistryComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'registry/:subjectType/:expertFirmId',
    data: { name: 'expert-firm' },
    component: ExpertFirmInfoComponent,
    canActivate: [AccessRightsGuard]
  },
  {
    path: 'appraisal-migration/:appraisalId/:wizardCode/:taskLockingUser',
    data: { name: 'appraisal-migration' },
    component: AppraisalMigrationComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'appraisal-ctu/:appraisalId/:wizardCode/:taskLockingUser',
    data: { name: 'appraisal-ctu' },
    component: AppraisalCtuComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'external-mortage',
    data: { name: 'external-mortage' },
    component: ExternalMortageComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'external-mortage-process/:requestId',
    data: { name: 'external-mortage-process' },
    component: ExternalMortageProcessComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'sampleChecks',
    data: { name: 'sampleChecks' },
    // component: SampleChecksTableComponent,
    canActivate: [AuthGuard, AccessRightsGuard],
    children: [
      {
        path: 'dashboard',
        data: { name: 'LoanAdministationSampleChecksComponent' },
        component: LoanAdministationSampleChecksComponent,
        canActivate: [AuthGuard, AccessRightsGuard]
      },
      {
        path: 'table',
        data: { name: 'SampleChecksTable' },
        component: SampleChecksTableComponent,
        canActivate: [AuthGuard, AccessRightsGuard]
      }
    ]
  },
  {
    path: 'document-suspend/:positionId',
    data: { name: 'document-suspend' },
    component: DocumentSuspendComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'goPrz2.do',
    component: RedirectComponent,
    canActivate: [AuthGuard]
  },
  //----------------------------SEARCH-SERVICING---------------------------------------------
  {
    path: 'search-servicing',
    data: { name: 'search-servicing' },
    component: SearchServicingComponent,
    canActivate: [AuthGuard, AccessRightsGuard]
  },
  {
    path: 'document',
    data: { name: 'document' },
    component: DocumentComponent,
    canActivate: [AuthGuard]
  },
  //------------------------------------------------------------------------------------------

 

// ---------------------NEW SILOS INTERFACE TEST------------------------------

// { path:'silos-interface',data: { name: 'silos-interface' }, component: SilosInterfaceComponent },

// -----------------------------------------------------------------------------
  { path: '**', redirectTo: '/index' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { useHash: false })],
  exports: [RouterModule]
})
export class AppRoutingModule {}
